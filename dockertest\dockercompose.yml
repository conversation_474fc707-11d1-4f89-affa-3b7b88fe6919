services:
  openresty:
    image: openresty/openresty:alpine
    container_name: openresty
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./openresty/conf/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf:ro
      - ./openresty/conf.d:/etc/nginx/conf.d:ro
      - ./openresty/ssl:/etc/nginx/ssl:ro
      - ./openresty/lua:/usr/local/openresty/nginx/lua:ro
    environment:
      - TZ=Asia/Shanghai
    logging:
      driver: json-file
      options:
        max-size: "200m"
        max-file: "3"
        compress: "true"
        labels: "production"
        env: "NGINX_VERSION"
        tag: "{{.Name}}/{{.ID}}"
    restart: always
    networks:
      - proxy_network
  veloera:
    image: ghcr.io/veloera/veloera:latest
    container_name: veloera
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./veloera/data:/data
      - ./veloera/logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    networks:
      - proxy_network
  3xui:
    image: ghcr.io/xeefei/3x-ui
    container_name: 3x-ui
    volumes:
      - ./3xui/db/:/etc/x-ui/
      - ./3xui/cert/:/root/cert/
    environment:
      XRAY_VMESS_AEAD_FORCED: "false"
      XUI_ENABLE_FAIL2BAN: "true"
    tty: true
    network_mode: host
    restart: unless-stopped

  openlist:
    image: 'openlistteam/openlist:latest'
    container_name: openlist
    volumes:
      - ./openlist:/opt/openlist/data
    ports:
      - '5344:5244'
    environment:
      - PUID=0
      - PGID=0
      - UMASK=022
    restart: unless-stopped
    networks:
      - proxy_network
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./notification-template.txt:/templates/notification-template.txt:ro
    command:
      - --interval
      - "43200"
      - --cleanup
      - --run-once-on-startup
      - openresty # 只监控 openresty 容器
      - openlist # 只监控 openlist 容器
    environment:
      - WATCHTOWER_NOTIFICATIONS=shoutrrr
      - WATCHTOWER_NOTIFICATION_URL=telegram://**********************************************@telegram?chats=5147678880&parseMode=HTML
      - WATCHTOWER_NOTIFICATION_REPORT=true
      - WATCHTOWER_NOTIFICATIONS_HOSTNAME=Docker-Server
      - WATCHTOWER_NOTIFICATION_TITLE_TAG=[容器更新]
      - WATCHTOWER_NOTIFICATION_TEMPLATE_FILE=/templates/notification-template.txt
      - WATCHTOWER_DEBUG=true
    restart: always
    networks:
      - proxy_network
networks:
  proxy_network:
    name: proxy_network

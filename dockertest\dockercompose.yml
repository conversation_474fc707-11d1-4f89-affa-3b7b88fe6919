services:
  openresty:
    image: openresty/openresty:alpine
    container_name: openresty
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./openresty/conf/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf:ro
      - ./openresty/conf.d:/etc/nginx/conf.d:ro
      - ./openresty/ssl:/etc/nginx/ssl:ro
      - ./openresty/lua:/usr/local/openresty/nginx/lua:ro
    environment:
      - TZ=Asia/Shanghai
    logging:
      driver: json-file
      options:
        max-size: "200m"
        max-file: "3"
        compress: "true"
        labels: "production"
        env: "NGINX_VERSION"
        tag: "{{.Name}}/{{.ID}}"
    restart: always
    networks:
      - proxy_network
  veloera:
    image: ghcr.io/veloera/veloera:latest
    container_name: veloera
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./veloera/data:/data
      - ./veloera/logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    networks:
      - proxy_network
  3xui:
    image: ghcr.io/xeefei/3x-ui
    container_name: 3x-ui
    volumes:
      - ./3xui/db/:/etc/x-ui/
      - ./3xui/cert/:/root/cert/
    environment:
      XRAY_VMESS_AEAD_FORCED: "false"
      XUI_ENABLE_FAIL2BAN: "true"
    tty: true
    network_mode: host
    restart: unless-stopped

  openlist:
    image: 'openlistteam/openlist:latest'
    container_name: openlist
    volumes:
      - ./openlist:/opt/openlist/data
    ports:
      - '5344:5244'
    environment:
      - PUID=0
      - PGID=0
      - UMASK=022
    restart: unless-stopped
    networks:
      - proxy_network
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command:
      - --interval
      - "43200"
      - --cleanup
      - --run-once-on-startup
      - openresty # 只监控 openresty 容器
      - openlist # 只监控 openlist 容器
    environment:
      WATCHTOWER_NOTIFICATIONS: shoutrrr
      WATCHTOWER_NOTIFICATION_URL: telegram://**********************************************@telegram?chats=5147678880&parseMode=HTML
      WATCHTOWER_NOTIFICATION_REPORT: "true"
      WATCHTOWER_INCLUDE_STOPPED: "true"
      WATCHTOWER_NOTIFICATIONS_HOSTNAME: Docker-Server
      WATCHTOWER_NOTIFICATION_TITLE_TAG: "[容器更新]"
      WATCHTOWER_WARN_ON_HEAD_FAILURE: never
      WATCHTOWER_INCLUDE_RESTARTING: "true"
      WATCHTOWER_NOTIFICATION_TEMPLATE: |
        {{- if .Report -}}
          {{- with .Report -}}
        🔔 <b>Watchtower 容器监控报告</b>
        🖥️ <b>服务器:</b> Docker-Server
        🕒 <b>扫描时间:</b> {{.Time.Format "2006-01-02 15:04:05"}}

        📊 <b>扫描统计:</b>
        • 已扫描: {{len .Scanned}} 个容器
        • 已更新: {{len .Updated}} 个容器
        • 更新失败: {{len .Failed}} 个容器
        • 保持最新: {{len .Fresh}} 个容器

        {{- if .Updated}}

        ✅ <b>成功更新的容器:</b>
        {{- range .Updated}}
        • <code>{{.Name}}</code>
          📦 镜像: <code>{{.ImageName}}</code>
          🔄 从 <code>{{.CurrentImageID.ShortID}}</code> 更新到 <code>{{.LatestImageID.ShortID}}</code>
        {{- end}}
        {{- end}}

        {{- if .Fresh}}

        ✨ <b>已是最新版本的容器:</b>
        {{- range .Fresh}}
        • <code>{{.Name}}</code>
          📦 镜像: <code>{{.ImageName}}</code>
          🏷️ 当前版本: <code>{{.CurrentImageID.ShortID}}</code>
        {{- end}}
        {{- end}}

        {{- if .Skipped}}

        ⏭️ <b>跳过的容器:</b>
        {{- range .Skipped}}
        • <code>{{.Name}}</code>: {{.State}} - {{.Error}}
        {{- end}}
        {{- end}}

        {{- if .Failed}}

        ❌ <b>更新失败的容器:</b>
        {{- range .Failed}}
        • <code>{{.Name}}</code>: {{.State}} - {{.Error}}
        {{- end}}
        {{- end}}

        {{- if and (eq (len .Updated) 0) (eq (len .Failed) 0) (gt (len .Fresh) 0)}}

        ✅ <b>所有监控的容器都是最新版本！</b>
        {{- end}}
          {{- end -}}
        {{- else -}}
        🔔 <b>Watchtower 启动通知</b>
        🖥️ <b>服务器:</b> Docker-Server
        🕒 <b>时间:</b> {{now.Format "2006-01-02 15:04:05"}}

        📝 <b>启动信息:</b>
        {{range .Entries -}}• {{.Message}}{{"\n"}}{{- end -}}
        {{- end -}}
    restart: always
    networks:
      - proxy_network
networks:
  proxy_network:
    name: proxy_network

🔔 <b>Watchtower 报告</b>
🖥️ <b>服务器:</b> Docker-Server

{{- if .Report -}}
  {{- with .Report -}}
📊 <b>统计:</b> 扫描{{len .Scanned}} 更新{{len .Updated}} 失败{{len .Failed}}

{{- range .Updated}}
✅ <b>已更新:</b> {{.Name}} ({{.ImageName}})
{{- end}}

{{- range .Stale}}
🆕 <b>可更新:</b> {{.Name}} ({{.ImageName}})
{{- end}}

{{- range .Fresh}}
✨ <b>最新:</b> {{.Name}} ({{.ImageName}})
{{- end}}

{{- range .Failed}}
❌ <b>失败:</b> {{.Name}} - {{.Error}}
{{- end}}
  {{- end -}}
{{- else -}}
📝 <b>启动信息:</b>
{{range .Entries -}}{{.Message}}{{"\n"}}{{- end -}}
{{- end -}}

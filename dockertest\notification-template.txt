{{- if .Report -}}
  {{- with .Report -}}
🔔 <b>Watchtower 容器监控报告</b>
🖥️ <b>服务器:</b> Docker-Server
🕒 <b>扫描时间:</b> {{.Time.Format "2006-01-02 15:04:05"}}

📊 <b>扫描统计:</b>
• 已扫描: {{len .Scanned}} 个容器
• 已更新: {{len .Updated}} 个容器
• 更新失败: {{len .Failed}} 个容器
• 保持最新: {{len .Fresh}} 个容器

{{- if .Updated}}

✅ <b>成功更新的容器:</b>
{{- range .Updated}}
• <code>{{.Name}}</code>
  📦 镜像: <code>{{.ImageName}}</code>
  🔄 从 <code>{{.CurrentImageID.ShortID}}</code> 更新到 <code>{{.LatestImageID.ShortID}}</code>
{{- end}}
{{- end}}

{{- if .Fresh}}

✨ <b>已是最新版本的容器:</b>
{{- range .Fresh}}
• <code>{{.Name}}</code>
  📦 镜像: <code>{{.ImageName}}</code>
  🏷️ 当前版本: <code>{{.CurrentImageID.ShortID}}</code>
{{- end}}
{{- end}}

{{- if .Skipped}}

⏭️ <b>跳过的容器:</b>
{{- range .Skipped}}
• <code>{{.Name}}</code>: {{.State}} - {{.Error}}
{{- end}}
{{- end}}

{{- if .Failed}}

❌ <b>更新失败的容器:</b>
{{- range .Failed}}
• <code>{{.Name}}</code>: {{.State}} - {{.Error}}
{{- end}}
{{- end}}

{{- if and (eq (len .Updated) 0) (eq (len .Failed) 0) (gt (len .Fresh) 0)}}

✅ <b>所有监控的容器都是最新版本！</b>
{{- end}}
  {{- end -}}
{{- else -}}
🔔 <b>Watchtower 启动通知</b>
🖥️ <b>服务器:</b> Docker-Server

📝 <b>启动信息:</b>
{{range .Entries -}}• {{.Message}}{{"\n"}}{{- end -}}
{{- end -}}
